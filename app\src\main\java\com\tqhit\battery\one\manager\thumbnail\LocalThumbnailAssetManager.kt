package com.tqhit.battery.one.manager.thumbnail

import android.content.Context
import android.content.res.AssetManager
import com.tqhit.battery.one.fragment.main.animation.data.AnimationItem
import com.tqhit.battery.one.utils.BatteryLogger
import dagger.hilt.android.qualifiers.ApplicationContext
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manages local thumbnail assets for immediate fallback during cold start.
 * Provides anime category thumbnails from app assets when network thumbnails are not available.
 * 
 * Following SOLID principles:
 * - Single Responsibility: Only handles local asset thumbnail management
 * - Open/Closed: Extensible for different categories and asset types
 * - Dependency Inversion: Depends on Android Context abstraction
 */
@Singleton
class LocalThumbnailAssetManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    companion object {
        private const val TAG = "LocalThumbnailAssetManager"
        private const val ANIME_ASSETS_PATH = "thumbnails/anime"
        private const val FALLBACK_THUMBNAIL_COUNT = 4
        
        // Local asset file names (these should be actual image files in production)
        private val ANIME_ASSET_FILES = listOf(
            "anime_thumbnail_1.jpg",
            "anime_thumbnail_2.jpg", 
            "anime_thumbnail_3.jpg",
            "anime_thumbnail_4.jpg"
        )
        
        // Fallback placeholder files (temporary until real images are added)
        private val ANIME_PLACEHOLDER_FILES = listOf(
            "anime_thumbnail_1.txt",
            "anime_thumbnail_2.txt",
            "anime_thumbnail_3.txt", 
            "anime_thumbnail_4.txt"
        )
    }
    
    private val assetManager: AssetManager = context.assets
    private var localAnimeAssets: List<String>? = null
    
    /**
     * Gets local anime thumbnail assets for immediate display during cold start.
     * Returns asset:// URIs that can be loaded by Glide.
     */
    fun getLocalAnimeThumbnails(): List<String> {
        if (localAnimeAssets == null) {
            localAnimeAssets = loadLocalAnimeThumbnails()
        }
        return localAnimeAssets ?: emptyList()
    }
    
    /**
     * Loads and validates local anime thumbnail assets.
     */
    private fun loadLocalAnimeThumbnails(): List<String> {
        val availableAssets = mutableListOf<String>()
        
        try {
            BatteryLogger.d(TAG, "Loading local anime thumbnail assets from $ANIME_ASSETS_PATH")
            
            // First try to load actual image files
            ANIME_ASSET_FILES.forEach { fileName ->
                val assetPath = "$ANIME_ASSETS_PATH/$fileName"
                if (assetExists(assetPath)) {
                    availableAssets.add("file:///android_asset/$assetPath")
                    BatteryLogger.d(TAG, "Found anime asset: $assetPath")
                }
            }
            
            // If no image files found, use placeholder files for development
            if (availableAssets.isEmpty()) {
                BatteryLogger.w(TAG, "No image assets found, using placeholder files for development")
                ANIME_PLACEHOLDER_FILES.forEach { fileName ->
                    val assetPath = "$ANIME_ASSETS_PATH/$fileName"
                    if (assetExists(assetPath)) {
                        // For placeholders, we'll use a default drawable instead
                        availableAssets.add("placeholder_$fileName")
                        BatteryLogger.d(TAG, "Found placeholder asset: $assetPath")
                    }
                }
            }
            
            BatteryLogger.d(TAG, "Loaded ${availableAssets.size} local anime thumbnail assets")
            
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error loading local anime thumbnail assets", e)
        }
        
        return availableAssets
    }
    
    /**
     * Checks if an asset file exists.
     */
    private fun assetExists(assetPath: String): Boolean {
        return try {
            assetManager.open(assetPath).use { true }
        } catch (e: IOException) {
            false
        }
    }
    
    /**
     * Creates fallback AnimationItem objects using local assets.
     * Used when Firebase Remote Config data is not available during cold start.
     */
    fun createFallbackAnimeItems(): List<AnimationItem> {
        val localThumbnails = getLocalAnimeThumbnails()
        
        return localThumbnails.mapIndexed { index, assetPath ->
            AnimationItem(
                isPremium = false,
                mediaOriginal = "local_asset_anime_$index", // Placeholder media URL
                thumbnail = assetPath
            )
        }
    }
    
    /**
     * Checks if a thumbnail URL is a local asset.
     */
    fun isLocalAsset(thumbnailUrl: String): Boolean {
        return thumbnailUrl.startsWith("file:///android_asset/") || 
               thumbnailUrl.startsWith("placeholder_")
    }
    
    /**
     * Gets the count of available local anime thumbnails.
     */
    fun getLocalAnimeThumbnailCount(): Int {
        return getLocalAnimeThumbnails().size
    }
    
    /**
     * Logs local asset availability for debugging.
     */
    fun logAssetAvailability() {
        val localThumbnails = getLocalAnimeThumbnails()
        BatteryLogger.d(TAG, "LOCAL_ASSET_DEBUG: Available local anime thumbnails: ${localThumbnails.size}")
        
        localThumbnails.forEachIndexed { index, assetPath ->
            BatteryLogger.d(TAG, "LOCAL_ASSET_DEBUG: [$index] $assetPath")
        }
    }
}
