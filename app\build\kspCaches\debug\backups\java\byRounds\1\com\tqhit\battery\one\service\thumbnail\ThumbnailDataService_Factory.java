package com.tqhit.battery.one.service.thumbnail;

import com.tqhit.battery.one.manager.thumbnail.LocalThumbnailAssetManager;
import com.tqhit.battery.one.service.animation.AnimationDataService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ThumbnailDataService_Factory implements Factory<ThumbnailDataService> {
  private final Provider<AnimationDataService> animationDataServiceProvider;

  private final Provider<LocalThumbnailAssetManager> localThumbnailAssetManagerProvider;

  public ThumbnailDataService_Factory(Provider<AnimationDataService> animationDataServiceProvider,
      Provider<LocalThumbnailAssetManager> localThumbnailAssetManagerProvider) {
    this.animationDataServiceProvider = animationDataServiceProvider;
    this.localThumbnailAssetManagerProvider = localThumbnailAssetManagerProvider;
  }

  @Override
  public ThumbnailDataService get() {
    return newInstance(animationDataServiceProvider.get(), localThumbnailAssetManagerProvider.get());
  }

  public static ThumbnailDataService_Factory create(
      Provider<AnimationDataService> animationDataServiceProvider,
      Provider<LocalThumbnailAssetManager> localThumbnailAssetManagerProvider) {
    return new ThumbnailDataService_Factory(animationDataServiceProvider, localThumbnailAssetManagerProvider);
  }

  public static ThumbnailDataService newInstance(AnimationDataService animationDataService,
      LocalThumbnailAssetManager localThumbnailAssetManager) {
    return new ThumbnailDataService(animationDataService, localThumbnailAssetManager);
  }
}
