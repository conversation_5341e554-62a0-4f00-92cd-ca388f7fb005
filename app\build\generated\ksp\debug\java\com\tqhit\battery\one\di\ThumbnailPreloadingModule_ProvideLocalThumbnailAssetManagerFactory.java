package com.tqhit.battery.one.di;

import android.content.Context;
import com.tqhit.battery.one.manager.thumbnail.LocalThumbnailAssetManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ThumbnailPreloadingModule_ProvideLocalThumbnailAssetManagerFactory implements Factory<LocalThumbnailAssetManager> {
  private final Provider<Context> contextProvider;

  public ThumbnailPreloadingModule_ProvideLocalThumbnailAssetManagerFactory(
      Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public LocalThumbnailAssetManager get() {
    return provideLocalThumbnailAssetManager(contextProvider.get());
  }

  public static ThumbnailPreloadingModule_ProvideLocalThumbnailAssetManagerFactory create(
      Provider<Context> contextProvider) {
    return new ThumbnailPreloadingModule_ProvideLocalThumbnailAssetManagerFactory(contextProvider);
  }

  public static LocalThumbnailAssetManager provideLocalThumbnailAssetManager(Context context) {
    return Preconditions.checkNotNullFromProvides(ThumbnailPreloadingModule.INSTANCE.provideLocalThumbnailAssetManager(context));
  }
}
