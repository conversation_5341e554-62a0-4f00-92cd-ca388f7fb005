@echo off
REM TJ_BatteryOne Cold Start Thumbnail Debug Script
REM Focuses on app cold start and thumbnail loading timing issues

echo ========================================
echo TJ_BatteryOne Cold Start Thumbnail Debug
echo ========================================
echo This script will:
echo 1. Force stop the app
echo 2. Clear logcat buffer
echo 3. Start cold start timing measurement
echo 4. Monitor thumbnail loading during startup
echo ========================================

REM Set ADB path and app ID
set ADB_PATH=E:\IDE\Android\SDK\platform-tools\adb.exe
set APP_ID=com.fc.p.tj.charginganimation.batterycharging.chargeeffect

echo Step 1: Force stopping app...
%ADB_PATH% shell am force-stop %APP_ID%
timeout /t 2 /nobreak >nul

echo Step 2: Clearing logcat buffer...
%ADB_PATH% logcat -c

echo Step 3: Starting logcat monitoring in background...
start "Logcat Monitor" %ADB_PATH% logcat -s ^
STARTUP_TIMING:* ^
DEFERRED_THUMBNAIL_PRELOAD:* ^
ThumbnailDataService:* ^
AnimationGridFragment:* ^
ANIMATION_LOADING_FIX:* ^
AnimationAdapter:* ^
BatteryApplication:* ^
MainActivity:* ^
ServiceManager:* ^
FirebaseRemoteConfigHelper:*

echo Step 4: Waiting 3 seconds for logcat to start...
timeout /t 3 /nobreak >nul

echo Step 5: Starting cold start with timing measurement...
echo ========================================
echo COLD START INITIATED - Monitor the logcat window
echo ========================================
%ADB_PATH% shell am start -W -n %APP_ID%/com.tqhit.battery.one.activity.splash.SplashActivity

echo.
echo Cold start completed. Check the logcat window for:
echo - STARTUP_TIMING logs for performance metrics
echo - DEFERRED_THUMBNAIL_PRELOAD logs for preloading status
echo - ANIMATION_LOADING_FIX logs for fragment initialization
echo - ThumbnailDataService logs for data availability
echo.
echo Press any key to stop monitoring...
pause >nul

echo Stopping logcat monitoring...
taskkill /f /fi "WindowTitle eq Logcat Monitor*" >nul 2>&1

echo Debug session completed.
pause
