package com.tqhit.battery.one.manager.thumbnail;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class LocalThumbnailAssetManager_Factory implements Factory<LocalThumbnailAssetManager> {
  private final Provider<Context> contextProvider;

  public LocalThumbnailAssetManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public LocalThumbnailAssetManager get() {
    return newInstance(contextProvider.get());
  }

  public static LocalThumbnailAssetManager_Factory create(Provider<Context> contextProvider) {
    return new LocalThumbnailAssetManager_Factory(contextProvider);
  }

  public static LocalThumbnailAssetManager newInstance(Context context) {
    return new LocalThumbnailAssetManager(context);
  }
}
