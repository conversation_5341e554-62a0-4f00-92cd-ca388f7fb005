@echo off
REM TJ_BatteryOne Anime Thumbnail Loading Debug Script
REM This script monitors specific logcat tags to debug thumbnail loading issues
REM Focus on the first 3 thumbnails in Anime category not displaying on startup

echo ========================================
echo TJ_BatteryOne Anime Thumbnail Debug
echo ========================================
echo Application ID: com.fc.p.tj.charginganimation.batterycharging.chargeeffect
echo Device: emulator-5554
echo Focus: First 3 Anime thumbnails not displaying on startup
echo ========================================

REM Set ADB path
set ADB_PATH=E:\IDE\Android\SDK\platform-tools\adb.exe

REM Check if device is connected
echo Checking device connection...
%ADB_PATH% devices
if %ERRORLEVEL% neq 0 (
    echo ERROR: ADB not found or device not connected
    pause
    exit /b 1
)

echo.
echo Starting comprehensive thumbnail loading monitoring...
echo Press Ctrl+C to stop monitoring
echo.

REM Clear logcat buffer first
%ADB_PATH% logcat -c

REM Start monitoring with multiple relevant tags
%ADB_PATH% logcat -s ^
ThumbnailDataService:* ^
DeferredThumbnailPreloading:* ^
AnimationAdapter:* ^
ThumbnailPreloader:* ^
ThumbnailPreloadingRepository:* ^
AnimationDataService:* ^
ANIMATION_LOADING_FIX:* ^
DEFERRED_THUMBNAIL_PRELOAD:* ^
STARTUP_TIMING:* ^
BatteryApplication:* ^
MainActivity:* ^
AnimationGridFragment:* ^
CategoryAdapter:* ^
FirebaseRemoteConfigHelper:* ^
Glide:* ^
ServiceManager:* ^
CoreBatteryStatsService:*

pause
