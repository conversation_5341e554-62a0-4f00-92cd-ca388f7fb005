package com.tqhit.battery.one.fragment.main.animation;

import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper;
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager;
import com.tqhit.battery.one.manager.thumbnail.LocalThumbnailAssetManager;
import com.tqhit.battery.one.repository.AppRepository;
import com.tqhit.battery.one.repository.ThumbnailPreloadingRepository;
import com.tqhit.battery.one.utils.PreloadingMonitor;
import com.tqhit.battery.one.utils.VideoUtils;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AnimationGridFragment_MembersInjector implements MembersInjector<AnimationGridFragment> {
  private final Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider;

  private final Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider;

  private final Provider<AppRepository> appRepositoryProvider;

  private final Provider<VideoUtils> videoUtilsProvider;

  private final Provider<PreloadingMonitor> preloadingMonitorProvider;

  private final Provider<ThumbnailPreloadingRepository> thumbnailPreloadingRepositoryProvider;

  private final Provider<LocalThumbnailAssetManager> localThumbnailAssetManagerProvider;

  public AnimationGridFragment_MembersInjector(
      Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider,
      Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider,
      Provider<AppRepository> appRepositoryProvider, Provider<VideoUtils> videoUtilsProvider,
      Provider<PreloadingMonitor> preloadingMonitorProvider,
      Provider<ThumbnailPreloadingRepository> thumbnailPreloadingRepositoryProvider,
      Provider<LocalThumbnailAssetManager> localThumbnailAssetManagerProvider) {
    this.applovinInterstitialAdManagerProvider = applovinInterstitialAdManagerProvider;
    this.remoteConfigHelperProvider = remoteConfigHelperProvider;
    this.appRepositoryProvider = appRepositoryProvider;
    this.videoUtilsProvider = videoUtilsProvider;
    this.preloadingMonitorProvider = preloadingMonitorProvider;
    this.thumbnailPreloadingRepositoryProvider = thumbnailPreloadingRepositoryProvider;
    this.localThumbnailAssetManagerProvider = localThumbnailAssetManagerProvider;
  }

  public static MembersInjector<AnimationGridFragment> create(
      Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider,
      Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider,
      Provider<AppRepository> appRepositoryProvider, Provider<VideoUtils> videoUtilsProvider,
      Provider<PreloadingMonitor> preloadingMonitorProvider,
      Provider<ThumbnailPreloadingRepository> thumbnailPreloadingRepositoryProvider,
      Provider<LocalThumbnailAssetManager> localThumbnailAssetManagerProvider) {
    return new AnimationGridFragment_MembersInjector(applovinInterstitialAdManagerProvider, remoteConfigHelperProvider, appRepositoryProvider, videoUtilsProvider, preloadingMonitorProvider, thumbnailPreloadingRepositoryProvider, localThumbnailAssetManagerProvider);
  }

  @Override
  public void injectMembers(AnimationGridFragment instance) {
    injectApplovinInterstitialAdManager(instance, applovinInterstitialAdManagerProvider.get());
    injectRemoteConfigHelper(instance, remoteConfigHelperProvider.get());
    injectAppRepository(instance, appRepositoryProvider.get());
    injectVideoUtils(instance, videoUtilsProvider.get());
    injectPreloadingMonitor(instance, preloadingMonitorProvider.get());
    injectThumbnailPreloadingRepository(instance, thumbnailPreloadingRepositoryProvider.get());
    injectLocalThumbnailAssetManager(instance, localThumbnailAssetManagerProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment.applovinInterstitialAdManager")
  public static void injectApplovinInterstitialAdManager(AnimationGridFragment instance,
      ApplovinInterstitialAdManager applovinInterstitialAdManager) {
    instance.applovinInterstitialAdManager = applovinInterstitialAdManager;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment.remoteConfigHelper")
  public static void injectRemoteConfigHelper(AnimationGridFragment instance,
      FirebaseRemoteConfigHelper remoteConfigHelper) {
    instance.remoteConfigHelper = remoteConfigHelper;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment.appRepository")
  public static void injectAppRepository(AnimationGridFragment instance,
      AppRepository appRepository) {
    instance.appRepository = appRepository;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment.videoUtils")
  public static void injectVideoUtils(AnimationGridFragment instance, VideoUtils videoUtils) {
    instance.videoUtils = videoUtils;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment.preloadingMonitor")
  public static void injectPreloadingMonitor(AnimationGridFragment instance,
      PreloadingMonitor preloadingMonitor) {
    instance.preloadingMonitor = preloadingMonitor;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment.thumbnailPreloadingRepository")
  public static void injectThumbnailPreloadingRepository(AnimationGridFragment instance,
      ThumbnailPreloadingRepository thumbnailPreloadingRepository) {
    instance.thumbnailPreloadingRepository = thumbnailPreloadingRepository;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment.localThumbnailAssetManager")
  public static void injectLocalThumbnailAssetManager(AnimationGridFragment instance,
      LocalThumbnailAssetManager localThumbnailAssetManager) {
    instance.localThumbnailAssetManager = localThumbnailAssetManager;
  }
}
