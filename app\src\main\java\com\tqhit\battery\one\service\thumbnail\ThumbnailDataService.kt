package com.tqhit.battery.one.service.thumbnail

import com.tqhit.battery.one.fragment.main.animation.data.AnimationCategory
import com.tqhit.battery.one.fragment.main.animation.data.ThumbnailItem
import com.tqhit.battery.one.service.animation.AnimationDataService
import com.tqhit.battery.one.manager.thumbnail.LocalThumbnailAssetManager
import com.tqhit.battery.one.utils.BatteryLogger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Service responsible for extracting thumbnail data from animation categories.
 * Specifically targets "Anime" and "Cartoon" categories for thumbnail preloading.
 * 
 * Following SOLID principles:
 * - Single Responsibility: Only handles thumbnail data extraction and filtering
 * - Open/Closed: Extensible for different category filtering strategies
 * - Dependency Inversion: Depends on abstractions (AnimationDataService)
 */
@Singleton
class ThumbnailDataService @Inject constructor(
    private val animationDataService: AnimationDataService,
    private val localThumbnailAssetManager: LocalThumbnailAssetManager
) {
    companion object {
        private const val TAG = "ThumbnailDataService"
        private const val THUMBNAILS_PER_CATEGORY = 4
        private val TARGET_CATEGORIES = setOf("Anime", "Cartoon")
    }
    
    /**
     * Fetches thumbnail items for preloading from target categories.
     * Returns the first 4 thumbnails from each "Anime" and "Cartoon" category.
     */
    suspend fun getThumbnailsForPreloading(): List<ThumbnailItem> = withContext(Dispatchers.IO) {
        try {
            BatteryLogger.d(TAG, "Fetching thumbnails for preloading from target categories: $TARGET_CATEGORIES")

            val allCategories = animationDataService.getAllAnimationCategories()
            if (allCategories.isEmpty()) {
                BatteryLogger.w(TAG, "No animation categories available for thumbnail extraction")
                return@withContext emptyList()
            }
            
            BatteryLogger.d(TAG, "Retrieved ${allCategories.size} total categories")
            
            // Filter target categories and extract thumbnails
            val thumbnailItems = mutableListOf<ThumbnailItem>()
            
            for (category in allCategories) {
                if (isTargetCategory(category.name)) {
                    BatteryLogger.d(TAG, "Processing target category: ${category.name} with ${category.content.size} animations")
                    
                    val categoryThumbnails = extractThumbnailsFromCategory(category)
                    thumbnailItems.addAll(categoryThumbnails)
                    
                    BatteryLogger.d(TAG, "Extracted ${categoryThumbnails.size} thumbnails from category: ${category.name}")
                } else {
                    BatteryLogger.d(TAG, "Skipping non-target category: ${category.name}")
                }
            }
            
            BatteryLogger.d(TAG, "Total thumbnails extracted for preloading: ${thumbnailItems.size}")
            
            // Log summary of extracted thumbnails
            logThumbnailExtractionSummary(thumbnailItems)
            
            thumbnailItems
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error fetching thumbnails for preloading", e)
            emptyList()
        }
    }

    /**
     * Fetches thumbnails with local asset fallback for immediate cold start display.
     * Prioritizes network thumbnails but falls back to local assets when data is not available.
     */
    suspend fun getThumbnailsWithLocalFallback(): List<ThumbnailItem> = withContext(Dispatchers.IO) {
        try {
            BatteryLogger.d(TAG, "COLD_START_FALLBACK: Fetching thumbnails with local asset fallback")

            // First try to get network thumbnails
            val networkThumbnails = getThumbnailsForPreloading()

            if (networkThumbnails.isNotEmpty()) {
                BatteryLogger.d(TAG, "COLD_START_FALLBACK: Using ${networkThumbnails.size} network thumbnails")
                return@withContext networkThumbnails
            }

            // Fall back to local assets for immediate display
            BatteryLogger.w(TAG, "COLD_START_FALLBACK: Network thumbnails not available, using local assets")
            val localAssets = localThumbnailAssetManager.getLocalAnimeThumbnails()

            val localThumbnails = localAssets.mapIndexed { index, assetPath ->
                ThumbnailItem(
                    thumbnailUrl = assetPath,
                    categoryName = "Anime",
                    animationMediaUrl = "local_asset_anime_$index",
                    isPremium = false
                )
            }

            BatteryLogger.d(TAG, "COLD_START_FALLBACK: Created ${localThumbnails.size} local asset thumbnails")
            localThumbnailAssetManager.logAssetAvailability()

            return@withContext localThumbnails

        } catch (e: Exception) {
            BatteryLogger.e(TAG, "COLD_START_FALLBACK: Error fetching thumbnails with fallback", e)
            return@withContext emptyList()
        }
    }

    /**
     * Extracts thumbnail items from a specific animation category.
     * Takes the first THUMBNAILS_PER_CATEGORY items from the category.
     */
    private fun extractThumbnailsFromCategory(category: AnimationCategory): List<ThumbnailItem> {
        return try {
            val validAnimations = category.content.filter { animation ->
                isValidThumbnailUrl(animation.thumbnail) && isValidMediaUrl(animation.mediaOriginal)
            }
            
            if (validAnimations.isEmpty()) {
                BatteryLogger.w(TAG, "No valid animations found in category: ${category.name}")
                return emptyList()
            }
            
            val thumbnailsToExtract = validAnimations.take(THUMBNAILS_PER_CATEGORY)
            
            thumbnailsToExtract.map { animation ->
                ThumbnailItem(
                    thumbnailUrl = animation.thumbnail,
                    categoryName = category.name,
                    animationMediaUrl = animation.mediaOriginal,
                    isPremium = animation.isPremium
                )
            }
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error extracting thumbnails from category: ${category.name}", e)
            emptyList()
        }
    }
    
    /**
     * Checks if a category name matches our target categories.
     * Uses case-insensitive matching for flexibility.
     */
    private fun isTargetCategory(categoryName: String): Boolean {
        return TARGET_CATEGORIES.any { targetCategory ->
            categoryName.equals(targetCategory, ignoreCase = true)
        }
    }
    
    /**
     * Validates if a thumbnail URL is properly formatted and not empty.
     */
    private fun isValidThumbnailUrl(thumbnailUrl: String): Boolean {
        return try {
            thumbnailUrl.isNotBlank() && 
            (thumbnailUrl.startsWith("http://") || thumbnailUrl.startsWith("https://")) &&
            (thumbnailUrl.contains(".jpg", ignoreCase = true) ||
             thumbnailUrl.contains(".jpeg", ignoreCase = true) ||
             thumbnailUrl.contains(".png", ignoreCase = true) ||
             thumbnailUrl.contains(".webp", ignoreCase = true) ||
             thumbnailUrl.contains(".gif", ignoreCase = true))
        } catch (e: Exception) {
            BatteryLogger.w(TAG, "Invalid thumbnail URL: $thumbnailUrl")
            false
        }
    }
    
    /**
     * Validates if a media URL is properly formatted and not empty.
     */
    private fun isValidMediaUrl(mediaUrl: String): Boolean {
        return try {
            mediaUrl.isNotBlank() && 
            (mediaUrl.startsWith("http://") || mediaUrl.startsWith("https://"))
        } catch (e: Exception) {
            BatteryLogger.w(TAG, "Invalid media URL: $mediaUrl")
            false
        }
    }
    
    /**
     * Logs a summary of thumbnail extraction for monitoring purposes.
     */
    private fun logThumbnailExtractionSummary(thumbnailItems: List<ThumbnailItem>) {
        try {
            val categoryGroups = thumbnailItems.groupBy { it.categoryName }
            
            BatteryLogger.d(TAG, "THUMBNAIL_EXTRACTION_SUMMARY: Total thumbnails: ${thumbnailItems.size}")
            
            categoryGroups.forEach { (categoryName, thumbnails) ->
                BatteryLogger.d(TAG, "THUMBNAIL_EXTRACTION_SUMMARY: Category '$categoryName': ${thumbnails.size} thumbnails")
                
                // Log first few thumbnail URLs for verification
                thumbnails.take(2).forEachIndexed { index, thumbnail ->
                    BatteryLogger.d(TAG, "THUMBNAIL_EXTRACTION_SUMMARY: $categoryName[$index]: ${thumbnail.thumbnailUrl}")
                }
            }
            
            val premiumCount = thumbnailItems.count { it.isPremium }
            val freeCount = thumbnailItems.size - premiumCount
            BatteryLogger.d(TAG, "THUMBNAIL_EXTRACTION_SUMMARY: Premium: $premiumCount, Free: $freeCount")
            
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error logging thumbnail extraction summary", e)
        }
    }
    
    /**
     * Gets all available category names from animation data.
     * Useful for debugging and configuration.
     */
    suspend fun getAvailableCategories(): List<String> = withContext(Dispatchers.IO) {
        try {
            val allCategories = animationDataService.getAllAnimationCategories()
            val categoryNames = allCategories.map { it.name }
            
            BatteryLogger.d(TAG, "Available categories: $categoryNames")
            categoryNames
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error getting available categories", e)
            emptyList()
        }
    }
    
    /**
     * Checks if target categories are available in the current animation data.
     * Returns true if at least one target category is found.
     */
    suspend fun areTargetCategoriesAvailable(): Boolean = withContext(Dispatchers.IO) {
        try {
            val availableCategories = getAvailableCategories()
            val hasTargetCategories = availableCategories.any { categoryName ->
                isTargetCategory(categoryName)
            }
            
            BatteryLogger.d(TAG, "Target categories available: $hasTargetCategories")
            
            if (!hasTargetCategories) {
                BatteryLogger.w(TAG, "No target categories found. Available: $availableCategories, Target: $TARGET_CATEGORIES")
            }
            
            hasTargetCategories
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error checking target category availability", e)
            false
        }
    }
    
    /**
     * Gets thumbnail count statistics for monitoring.
     */
    suspend fun getThumbnailStatistics(): Map<String, Int> = withContext(Dispatchers.IO) {
        try {
            val thumbnails = getThumbnailsForPreloading()
            val stats = mutableMapOf<String, Int>()
            
            stats["total"] = thumbnails.size
            stats["premium"] = thumbnails.count { it.isPremium }
            stats["free"] = thumbnails.count { !it.isPremium }
            
            // Per-category stats
            thumbnails.groupBy { it.categoryName }.forEach { (category, items) ->
                stats["category_$category"] = items.size
            }
            
            BatteryLogger.d(TAG, "Thumbnail statistics: $stats")
            stats
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error getting thumbnail statistics", e)
            emptyMap()
        }
    }
}
