package com.tqhit.battery.one.di

import android.content.Context
import com.google.gson.Gson
import com.tqhit.adlib.sdk.data.local.PreferencesHelper
import com.tqhit.battery.one.manager.thumbnail.ThumbnailFileManager
import com.tqhit.battery.one.manager.thumbnail.LocalThumbnailAssetManager
import com.tqhit.battery.one.repository.ThumbnailPreloadingRepository
import com.tqhit.battery.one.service.animation.AnimationDataService
import com.tqhit.battery.one.service.thumbnail.ThumbnailDataService
import com.tqhit.battery.one.service.thumbnail.ThumbnailPreloader
import com.tqhit.battery.one.service.thumbnail.DeferredThumbnailPreloadingService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Dagger Hilt module for thumbnail preloading dependency injection.
 * Provides bindings for all thumbnail preloading related components.
 * 
 * Following SOLID principles:
 * - Single Responsibility: Only handles thumbnail preloading DI configuration
 * - Open/Closed: Extensible for additional thumbnail preloading components
 * - Dependency Inversion: Provides abstractions and implementations
 */
@Module
@InstallIn(SingletonComponent::class)
object ThumbnailPreloadingModule {
    
    /**
     * Provides ThumbnailFileManager singleton.
     * Manages local file operations for preloaded thumbnails.
     * 
     * @param context Application context for file operations
     * @return ThumbnailFileManager instance
     */
    @Provides
    @Singleton
    fun provideThumbnailFileManager(
        @ApplicationContext context: Context
    ): ThumbnailFileManager {
        return ThumbnailFileManager(context)
    }
    
    /**
     * Provides ThumbnailPreloader singleton.
     * Handles concurrent thumbnail downloads and caching.
     * 
     * @param context Application context
     * @param fileManager ThumbnailFileManager for file operations
     * @return ThumbnailPreloader instance
     */
    @Provides
    @Singleton
    fun provideThumbnailPreloader(
        @ApplicationContext context: Context,
        fileManager: ThumbnailFileManager
    ): ThumbnailPreloader {
        return ThumbnailPreloader(context, fileManager)
    }
    
    /**
     * Provides LocalThumbnailAssetManager singleton.
     * Manages local thumbnail assets for immediate fallback during cold start.
     *
     * @param context Application context
     * @return LocalThumbnailAssetManager instance
     */
    @Provides
    @Singleton
    fun provideLocalThumbnailAssetManager(
        @ApplicationContext context: Context
    ): LocalThumbnailAssetManager {
        return LocalThumbnailAssetManager(context)
    }

    /**
     * Provides ThumbnailDataService singleton.
     * Extracts thumbnail data from animation categories.
     *
     * @param animationDataService Service for fetching animation data
     * @param localThumbnailAssetManager Manager for local thumbnail assets
     * @return ThumbnailDataService instance
     */
    @Provides
    @Singleton
    fun provideThumbnailDataService(
        animationDataService: AnimationDataService,
        localThumbnailAssetManager: LocalThumbnailAssetManager
    ): ThumbnailDataService {
        return ThumbnailDataService(animationDataService, localThumbnailAssetManager)
    }
    
    /**
     * Provides ThumbnailPreloadingRepository singleton.
     * Repository pattern for thumbnail preloading operations.
     * 
     * @param thumbnailPreloader Service for thumbnail preloading
     * @param fileManager Manager for thumbnail file operations
     * @param preferencesHelper Helper for storing preload metadata
     * @param gson JSON serialization for metadata persistence
     * @return ThumbnailPreloadingRepository instance
     */
    @Provides
    @Singleton
    fun provideThumbnailPreloadingRepository(
        thumbnailPreloader: ThumbnailPreloader,
        fileManager: ThumbnailFileManager,
        preferencesHelper: PreferencesHelper,
        gson: Gson
    ): ThumbnailPreloadingRepository {
        return ThumbnailPreloadingRepository(
            thumbnailPreloader,
            fileManager,
            preferencesHelper,
            gson
        )
    }

    /**
     * Provides DeferredThumbnailPreloadingService singleton.
     * Coordinates deferred thumbnail preloading after Firebase Remote Config is loaded.
     *
     * @param context Application context
     * @param animationDataService Service for fetching animation data
     * @param thumbnailDataService Service for extracting thumbnail data
     * @param thumbnailPreloadingRepository Repository for thumbnail preloading operations
     * @return DeferredThumbnailPreloadingService instance
     */
    @Provides
    @Singleton
    fun provideDeferredThumbnailPreloadingService(
        @ApplicationContext context: Context,
        animationDataService: AnimationDataService,
        thumbnailDataService: ThumbnailDataService,
        thumbnailPreloadingRepository: ThumbnailPreloadingRepository
    ): DeferredThumbnailPreloadingService {
        return DeferredThumbnailPreloadingService(
            context,
            animationDataService,
            thumbnailDataService,
            thumbnailPreloadingRepository
        )
    }
}
