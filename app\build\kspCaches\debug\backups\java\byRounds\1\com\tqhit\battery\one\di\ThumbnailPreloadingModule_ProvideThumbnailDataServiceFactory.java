package com.tqhit.battery.one.di;

import com.tqhit.battery.one.manager.thumbnail.LocalThumbnailAssetManager;
import com.tqhit.battery.one.service.animation.AnimationDataService;
import com.tqhit.battery.one.service.thumbnail.ThumbnailDataService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ThumbnailPreloadingModule_ProvideThumbnailDataServiceFactory implements Factory<ThumbnailDataService> {
  private final Provider<AnimationDataService> animationDataServiceProvider;

  private final Provider<LocalThumbnailAssetManager> localThumbnailAssetManagerProvider;

  public ThumbnailPreloadingModule_ProvideThumbnailDataServiceFactory(
      Provider<AnimationDataService> animationDataServiceProvider,
      Provider<LocalThumbnailAssetManager> localThumbnailAssetManagerProvider) {
    this.animationDataServiceProvider = animationDataServiceProvider;
    this.localThumbnailAssetManagerProvider = localThumbnailAssetManagerProvider;
  }

  @Override
  public ThumbnailDataService get() {
    return provideThumbnailDataService(animationDataServiceProvider.get(), localThumbnailAssetManagerProvider.get());
  }

  public static ThumbnailPreloadingModule_ProvideThumbnailDataServiceFactory create(
      Provider<AnimationDataService> animationDataServiceProvider,
      Provider<LocalThumbnailAssetManager> localThumbnailAssetManagerProvider) {
    return new ThumbnailPreloadingModule_ProvideThumbnailDataServiceFactory(animationDataServiceProvider, localThumbnailAssetManagerProvider);
  }

  public static ThumbnailDataService provideThumbnailDataService(
      AnimationDataService animationDataService,
      LocalThumbnailAssetManager localThumbnailAssetManager) {
    return Preconditions.checkNotNullFromProvides(ThumbnailPreloadingModule.INSTANCE.provideThumbnailDataService(animationDataService, localThumbnailAssetManager));
  }
}
