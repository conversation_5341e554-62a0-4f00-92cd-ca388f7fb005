@echo off
REM TJ_BatteryOne Local Asset Fallback Testing Script
REM Tests the local asset fallback mechanism for Anime thumbnails during cold start

echo ========================================
echo TJ_BatteryOne Local Asset Fallback Test
echo ========================================
echo This script will test:
echo 1. Local asset availability and loading
echo 2. Cold start with local asset fallback
echo 3. Network thumbnail fallback behavior
echo 4. Fragment initialization with local assets
echo ========================================

REM Set ADB path and app ID
set ADB_PATH=E:\IDE\Android\SDK\platform-tools\adb.exe
set APP_ID=com.fc.p.tj.charginganimation.batterycharging.chargeeffect

echo Step 1: Checking device connection...
%ADB_PATH% devices
if %ERRORLEVEL% neq 0 (
    echo ERROR: ADB not found or device not connected
    pause
    exit /b 1
)

echo.
echo Step 2: Force stopping app to ensure clean cold start...
%ADB_PATH% shell am force-stop %APP_ID%
timeout /t 2 /nobreak >nul

echo Step 3: Clearing logcat buffer...
%ADB_PATH% logcat -c

echo Step 4: Starting comprehensive logcat monitoring...
start "Local Asset Test Monitor" %ADB_PATH% logcat -s ^
COLD_START_FALLBACK:* ^
LOCAL_ASSET_DEBUG:* ^
LocalThumbnailAssetManager:* ^
ThumbnailDataService:* ^
AnimationAdapter:* ^
ANIMATION_LOADING_FIX:* ^
AnimationGridFragment:* ^
STARTUP_TIMING:* ^
DEFERRED_THUMBNAIL_PRELOAD:* ^
BatteryApplication:*

echo Step 5: Waiting for logcat to initialize...
timeout /t 3 /nobreak >nul

echo Step 6: Starting cold start test...
echo ========================================
echo COLD START WITH LOCAL ASSET FALLBACK TEST
echo ========================================
echo Monitor the logcat window for:
echo - COLD_START_FALLBACK logs for local asset usage
echo - LOCAL_ASSET_DEBUG logs for asset availability
echo - LocalThumbnailAssetManager logs for asset loading
echo - AnimationAdapter logs for thumbnail loading decisions
echo ========================================

%ADB_PATH% shell am start -W -n %APP_ID%/com.tqhit.battery.one.activity.splash.SplashActivity

echo.
echo Cold start completed. 
echo.
echo Step 7: Testing navigation to Animation fragment...
timeout /t 5 /nobreak >nul

echo Step 8: Checking for local asset usage in logs...
echo ========================================
echo Recent logs related to local assets:
echo ========================================
%ADB_PATH% logcat -d -s COLD_START_FALLBACK:* LOCAL_ASSET_DEBUG:* LocalThumbnailAssetManager:* | tail -20

echo.
echo ========================================
echo Test completed. Check the logcat monitor window for detailed logs.
echo Key indicators of success:
echo - "COLD_START_FALLBACK: Using local asset" messages
echo - "LOCAL_ASSET_DEBUG: Available local anime thumbnails" messages  
echo - "LocalThumbnailAssetManager: Found anime asset" messages
echo - No "Thumbnail load failed" errors for first 3-4 thumbnails
echo ========================================
echo.
echo Press any key to stop monitoring and exit...
pause >nul

echo Stopping logcat monitoring...
taskkill /f /fi "WindowTitle eq Local Asset Test Monitor*" >nul 2>&1

echo Test session completed.
pause
